{% extends "base.html" %}

{% block title %}Hostel Life Dashboard 🏠✨{% endblock %}

{% block extra_css %}
<style>
/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 3rem 2rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-illustration {
    position: relative;
    height: 80px;
    margin-bottom: 2rem;
}

.hostel-building {
    font-size: 4rem;
    animation: bounce 2s infinite;
}

.floating-emojis {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.emoji-float {
    position: absolute;
    font-size: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

.emoji-float:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
.emoji-float:nth-child(2) { top: 20%; right: 20%; animation-delay: 0.5s; }
.emoji-float:nth-child(3) { bottom: 20%; left: 30%; animation-delay: 1s; }
.emoji-float:nth-child(4) { bottom: 10%; right: 30%; animation-delay: 1.5s; }

.hero-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* Stat Cards */
.stat-card {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Attendance Card */
.attendance-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 20px;
    overflow: hidden;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.pulse-icon {
    animation: pulse 2s infinite;
}

.attendance-illustration {
    margin: 2rem 0;
}

.check-in-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: 2rem;
}

.email-icon, .success-icon {
    animation: bounce 2s infinite;
}

.arrow-flow {
    animation: slide 2s infinite;
}

.attendance-description {
    font-size: 1.1rem;
    line-height: 1.6;
}

.highlight {
    background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.fun-fact-box {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 1rem;
    border-left: 4px solid #ff8a80;
}

.fun-fact-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.fun-fact-text {
    font-size: 0.95rem;
    color: #5d4037;
}

/* Custom Buttons */
.btn-attendance-primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
    border-radius: 15px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-attendance-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 107, 107, 0.4);
    color: white;
}

.btn-hero-action {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-hero-action:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-icon, .btn-sparkle {
    font-size: 1.2rem;
}

.btn-text {
    margin: 0 0.5rem;
}

.quick-stats {
    margin-top: 1rem;
}

.stat-badge {
    background: rgba(40, 167, 69, 0.1);
    border: 2px solid rgba(40, 167, 69, 0.3);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #28a745;
    font-weight: 600;
}

.stat-emoji {
    font-size: 1.1rem;
}

/* Command Center */
.command-center-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 20px;
    overflow: hidden;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.command-icon {
    animation: rotate 4s linear infinite;
}

.command-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.command-button {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 15px;
    padding: 1.5rem 1rem;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    text-align: center;
    display: block;
}

.command-button:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    color: #495057;
    text-decoration: none;
}

.command-button.test-button:hover {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.command-button.admin-button:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
}

.command-button.stats-button:hover {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-color: #17a2b8;
}

.command-icon-large {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.command-text {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.command-subtitle {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* Hostel Wisdom */
.hostel-wisdom {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 1.5rem;
}

.wisdom-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.wisdom-icon {
    font-size: 1.5rem;
}

.wisdom-title {
    font-weight: 600;
    color: #5d4037;
}

.wisdom-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.wisdom-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    color: #5d4037;
}

.wisdom-emoji {
    font-size: 1.2rem;
}

/* Squad Status */
.squad-status-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 20px;
    overflow: hidden;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.squad-icon {
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

.squad-count {
    font-weight: normal;
    opacity: 0.9;
}

.attendance-badge {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

/* Empty State */
.empty-state {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    margin: 2rem;
}

.empty-state-animation {
    position: relative;
    height: 80px;
}

.sleeping-hostel {
    font-size: 3rem;
    animation: sleep 3s ease-in-out infinite;
}

.wake-up-call {
    position: absolute;
    top: -10px;
    right: 20px;
    font-size: 1.5rem;
    animation: ring 2s infinite;
}

.empty-state-title {
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-state-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.motivation-box {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 15px;
    padding: 1rem;
    border-left: 4px solid #ffc107;
}

.motivation-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.motivation-text {
    color: #856404;
    font-size: 0.95rem;
}

/* Achievements */
.achievements-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 20px;
    overflow: hidden;
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
}

.achievement-icon {
    font-size: 1.5rem;
    animation: sparkle 3s infinite;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.achievement-category {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem 1.5rem;
    text-align: center;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.achievement-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.achievement-category.streak-masters:hover {
    background: linear-gradient(135deg, #ffe6e6 0%, #ffcccc 100%);
    border-color: #dc3545;
}

.achievement-category.early-birds:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border-color: #ffc107;
}

.achievement-category.consistency-kings:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-color: #28a745;
}

.achievement-category.hostel-heroes:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #007bff;
}

.achievement-icon-large {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.achievement-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #495057;
}

.achievement-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.achievement-count {
    font-weight: 600;
    color: #007bff;
    font-size: 0.9rem;
}

.motivation-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
}

.motivation-content {
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
}

.motivation-emoji {
    font-size: 1.5rem;
    margin: 0 0.5rem;
}

.motivation-message {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes slide {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(5px); }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes sleep {
    0%, 100% { transform: rotate(-5deg); }
    50% { transform: rotate(5deg); }
}

@keyframes ring {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

@keyframes sparkle {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title { font-size: 2rem; }
    .hero-subtitle { font-size: 1rem; }
    .stat-card { padding: 1rem 0.5rem; }
    .command-grid { grid-template-columns: repeat(2, 1fr); }
    .achievements-grid { grid-template-columns: 1fr; }
}
</style>
{% endblock %}

{% block content %}
<!-- Hero Section with Engaging Visual -->
<div class="hero-section mb-4">
    <div class="hero-content text-center text-white">
        <div class="hero-illustration mb-3">
            <div class="hostel-building">
                🏢
            </div>
            <div class="floating-emojis">
                <span class="emoji-float">🎓</span>
                <span class="emoji-float">📚</span>
                <span class="emoji-float">🌟</span>
                <span class="emoji-float">🎯</span>
            </div>
        </div>
        <h1 class="hero-title">{{ welcome_message }}</h1>
        <p class="hero-subtitle">{{ today_date }} • {{ current_time }} • Your daily check-in adventure awaits! 🚀</p>

        <!-- Quick Stats Cards -->
        <div class="row mt-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">{{ total_students }}</div>
                    <div class="stat-label">Squad Members</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number">{{ attendance_count }}</div>
                    <div class="stat-label">Checked In</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-number">{{ attendance_percentage }}%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">🔥</div>
                    <div class="stat-number">{{ auth_stats.recent_attendance_24h or 0 }}</div>
                    <div class="stat-label">Today's Energy</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Daily Check-In Adventure -->
    <div class="col-lg-6">
        <div class="card attendance-card h-100">
            <div class="card-header bg-gradient-success text-white">
                <h5 class="card-title mb-0">
                    <span class="pulse-icon">⚡</span> Daily Check-In Adventure
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="attendance-illustration mb-3">
                    <div class="check-in-animation">
                        <div class="email-icon">📧</div>
                        <div class="arrow-flow">➡️</div>
                        <div class="success-icon">✨</div>
                    </div>
                </div>

                <h6 class="text-success mb-3">Ready to join today's squad? 🎯</h6>
                <p class="attendance-description">
                    Just enter your email twice and become part of today's awesome crew!
                    <span class="highlight">Super simple, super secure!</span> 🛡️
                </p>

                <div class="fun-fact-box mb-3">
                    <div class="fun-fact-icon">💡</div>
                    <div class="fun-fact-text">
                        <strong>Fun Fact:</strong> Students who check in daily are 85% more likely to have an amazing hostel experience! 🌟
                    </div>
                </div>

                <div class="d-grid gap-3">
                    <a href="{{ url_for('login_attendance') }}" class="btn btn-attendance-primary btn-lg">
                        <span class="btn-icon">🚀</span>
                        <span class="btn-text">Start Your Check-In Journey</span>
                        <span class="btn-sparkle">✨</span>
                    </a>

                    <div class="quick-stats">
                        <span class="stat-badge">
                            <span class="stat-emoji">🔥</span>
                            <span class="stat-text">{{ attendance_count }} checked in today</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hostel Command Center -->
    <div class="col-lg-6">
        <div class="card command-center-card h-100">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <span class="command-icon">🎮</span> Hostel Command Center
                </h5>
            </div>
            <div class="card-body">
                <div class="command-grid">
                    <a href="{{ url_for('admin') }}" class="command-button admin-button">
                        <div class="command-icon-large">👑</div>
                        <div class="command-text">Squad Manager</div>
                        <div class="command-subtitle">Admin powers</div>
                    </a>

                    <a href="{{ url_for('auth_stats_api') }}" class="command-button stats-button" target="_blank">
                        <div class="command-icon-large">📊</div>
                        <div class="command-text">Live Stats</div>
                        <div class="command-subtitle">Real-time data</div>
                    </a>
                </div>

                <div class="hostel-wisdom mt-4">
                    <div class="wisdom-header">
                        <span class="wisdom-icon">🧠</span>
                        <span class="wisdom-title">Hostel Wisdom</span>
                    </div>
                    <div class="wisdom-content">
                        <div class="wisdom-item">
                            <span class="wisdom-emoji">🌅</span>
                            <span class="wisdom-text">Early birds get the best breakfast!</span>
                        </div>
                        <div class="wisdom-item">
                            <span class="wisdom-emoji">🔥</span>
                            <span class="wisdom-text">Consistency builds legendary streaks!</span>
                        </div>
                        <div class="wisdom-item">
                            <span class="wisdom-emoji">🤝</span>
                            <span class="wisdom-text">Help your roommates check in too!</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Squad Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card squad-status-card">
            <div class="card-header bg-gradient-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="squad-icon">🏆</span> Today's Squad Status
                        <span class="squad-count">({{ attendance_count }}/{{ total_students }})</span>
                    </h5>
                    <div class="attendance-badge">
                        <span class="badge-icon">🎯</span>
                        <span class="badge-text">{{ attendance_percentage }}% Squad Power</span>
                    </div>
                </div>

                <!-- Admin Actions -->
                {% if attendance_count > 0 %}
                <div class="admin-actions">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <small class="text-light mb-2 mb-md-0">
                            <i class="fas fa-user-shield"></i> <strong>Admin Actions:</strong>
                        </small>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ url_for('export_attendance_pdf') }}"
                               class="btn btn-success btn-sm admin-button"
                               id="exportPdfBtn"
                               onclick="showExportLoading(this)">
                                <i class="fas fa-file-pdf"></i> Export to PDF
                            </a>
                            <button class="btn btn-danger btn-sm admin-button admin-warning" onclick="confirmResetAttendance()">
                                <i class="fas fa-trash-alt"></i> Reset Today's Attendance
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            <div class="card-body">
                {% if today_attendance %}
                    <div class="row">
                        {% for student in today_attendance %}
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light border-success student-card-admin">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="fas fa-user-check fa-2x text-success"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-1">{{ student.name }}</h6>
                                            <small class="text-muted">
                                                <i class="fas fa-door-open"></i> {{ student.room_no }} |
                                                <i class="fas fa-graduation-cap"></i> {{ student.branch }}
                                            </small>
                                            <br>
                                            <small class="text-success">
                                                <i class="fas fa-clock"></i>
                                                {% if student.check_in_formatted %}
                                                    {{ student.check_in_formatted }}
                                                {% elif student.check_in_time %}
                                                    {{ student.check_in_time.split(' ')[1][:5] if ' ' in student.check_in_time else student.check_in_time[:5] }}
                                                {% else %}
                                                    Just now
                                                {% endif %}
                                            </small>
                                        </div>
                                        <div class="d-flex flex-column align-items-end">
                                            <span class="badge bg-success mb-2">✓</span>
                                            <button class="btn btn-outline-danger btn-sm student-remove-btn"
                                                    onclick="confirmRemoveStudent({{ student.id }}, '{{ student.name }}')"
                                                    title="Remove {{ student.name }} from today's attendance">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    {% if attendance_count < total_students %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>{{ total_students - attendance_count }} students</strong> haven't checked in yet today!
                        Share the QR code to help them mark their attendance! 📱
                    </div>
                    {% endif %}

                {% else %}
                    <div class="empty-state text-center p-5">
                        <div class="empty-state-animation mb-4">
                            <div class="sleeping-hostel">🏢💤</div>
                            <div class="wake-up-call">⏰</div>
                        </div>
                        <h4 class="empty-state-title">The hostel is still sleeping! 😴</h4>
                        <p class="empty-state-subtitle">Be the hero who wakes everyone up and starts the daily adventure! 🦸‍♂️</p>

                        <div class="motivation-box mb-4">
                            <div class="motivation-icon">🌟</div>
                            <div class="motivation-text">
                                First check-in of the day gets the <strong>"Early Bird Champion"</strong> badge! 🏅
                            </div>
                        </div>

                        <a href="{{ url_for('login_attendance') }}" class="btn btn-hero-action btn-lg">
                            <span class="btn-icon">🚀</span>
                            <span class="btn-text">Wake Up The Hostel!</span>
                            <span class="btn-sparkle">✨</span>
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Hostel Achievements & Leaderboard -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card achievements-card">
            <div class="card-header bg-gradient-dark text-white">
                <h5 class="card-title mb-0">
                    <span class="achievement-icon">🏆</span> Hostel Hall of Fame
                </h5>
            </div>
            <div class="card-body">
                <div class="achievements-grid">
                    <div class="achievement-category streak-masters">
                        <div class="achievement-icon-large">🔥</div>
                        <div class="achievement-title">Streak Masters</div>
                        <div class="achievement-description">Legends who never miss a day!</div>
                        <div class="achievement-count">{{ total_students // 4 }} Champions</div>
                    </div>

                    <div class="achievement-category early-birds">
                        <div class="achievement-icon-large">🌅</div>
                        <div class="achievement-title">Early Birds</div>
                        <div class="achievement-description">First to check in daily!</div>
                        <div class="achievement-count">{{ total_students // 3 }} Heroes</div>
                    </div>

                    <div class="achievement-category consistency-kings">
                        <div class="achievement-icon-large">⭐</div>
                        <div class="achievement-title">Consistency Kings</div>
                        <div class="achievement-description">Reliable squad members!</div>
                        <div class="achievement-count">{{ total_students // 2 }} Stars</div>
                    </div>

                    <div class="achievement-category hostel-heroes">
                        <div class="achievement-icon-large">🦸‍♂️</div>
                        <div class="achievement-title">Hostel Heroes</div>
                        <div class="achievement-description">Ultimate attendance warriors!</div>
                        <div class="achievement-count">{{ attendance_count }} Active</div>
                    </div>
                </div>

                <div class="motivation-banner mt-4">
                    <div class="motivation-content">
                        <span class="motivation-emoji">💪</span>
                        <span class="motivation-message">
                            Join the Hall of Fame! Check in daily and become a hostel legend!
                        </span>
                        <span class="motivation-emoji">🌟</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh stats every 5 minutes
setInterval(function() {
    fetch('/api/auth_stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Auth stats updated:', data.stats);
                // Update stats display if needed
            }
        })
        .catch(err => console.log('Auth stats check failed:', err));
}, 300000); // 5 minutes

// Admin Functions
function confirmResetAttendance() {
    const attendanceCount = {{ attendance_count }};

    if (confirm(`⚠️ Are you sure you want to reset today's attendance?\n\nThis will remove all ${attendanceCount} attendance records for today.\n\n🚨 This action cannot be undone!`)) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/reset_attendance';

        // Add CSRF token if needed (Flask-WTF)
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

function confirmRemoveStudent(studentId, studentName) {
    if (confirm(`🤔 Remove ${studentName} from today's attendance?\n\nThis will delete their check-in record for today only.\n\n⚠️ This action cannot be undone!`)) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/remove_attendance/${studentId}`;

        // Add CSRF token if needed (Flask-WTF)
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// PDF Export Functions
function showExportLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
    button.classList.add('disabled');

    // Reset button after 5 seconds (in case of issues)
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('disabled');
    }, 5000);
}
</script>
{% endblock %}
