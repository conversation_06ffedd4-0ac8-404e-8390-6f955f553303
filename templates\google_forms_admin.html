{% extends "base.html" %}

{% block title %}Google Forms Integration - Coming Soon! 🚧{% endblock %}

{% block extra_css %}
<style>
    .coming-soon-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 25px;
        padding: 4rem 2rem;
        color: white;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;
    }

    .coming-soon-container::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 6s infinite;
    }

    .coming-soon-content {
        position: relative;
        z-index: 2;
    }

    .construction-animation {
        font-size: 5rem;
        margin-bottom: 2rem;
        animation: bounce 2s infinite;
    }

    .coming-soon-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .coming-soon-subtitle {
        font-size: 1.4rem;
        margin-bottom: 3rem;
        opacity: 0.9;
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin: 3rem 0;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 2rem;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.15);
    }

    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .feature-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .feature-description {
        font-size: 1rem;
        opacity: 0.9;
    }

    .back-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.5);
        color: white;
        padding: 1rem 2rem;
        border-radius: 20px;
        font-size: 1.2rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        margin-top: 2rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .back-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    @keyframes shimmer {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-20px); }
        60% { transform: translateY(-10px); }
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="coming-soon-container">
            <div class="coming-soon-content">
                <div class="construction-animation">🚧</div>
                <h1 class="coming-soon-title">Google Forms Integration</h1>
                <p class="coming-soon-subtitle">
                    Amazing features are being crafted! Coming soon to make your hostel experience even more magical! ✨
                </p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📋</div>
                        <div class="feature-title">Smart Forms</div>
                        <div class="feature-description">
                            Automatic attendance tracking through Google Forms with real-time sync
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <div class="feature-title">Live Analytics</div>
                        <div class="feature-description">
                            Real-time response monitoring and advanced attendance analytics
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🔗</div>
                        <div class="feature-title">Seamless Integration</div>
                        <div class="feature-description">
                            Perfect integration with existing email authentication system
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-title">Auto-Sync</div>
                        <div class="feature-description">
                            Automatic synchronization with Google Sheets for data management
                        </div>
                    </div>
                </div>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; margin: 2rem 0;">
                    <h4 style="margin-bottom: 1rem;">🎯 Current Status</h4>
                    <p style="font-size: 1.1rem; margin-bottom: 1rem;">
                        The email authentication system is fully operational and providing an amazing experience! 
                        Google Forms integration will enhance this even further.
                    </p>
                    <div style="background: rgba(40, 167, 69, 0.3); border-radius: 10px; padding: 1rem; margin-top: 1rem;">
                        <strong>✅ Email Authentication: Fully Operational</strong><br>
                        <strong>🚧 Google Forms: Under Development</strong>
                    </div>
                </div>

                <a href="{{ url_for('index') }}" class="back-btn">
                    <span style="font-size: 1.3rem; margin-right: 0.5rem;">🏠</span>
                    Back to Adventure Hub
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Add some interactive elements
document.querySelectorAll('.feature-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>
{% endblock %}
