<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Check-In Adventure 🚀</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        /* Floating Background Elements */
        .bg-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-emoji {
            position: absolute;
            font-size: 2rem;
            opacity: 0.1;
            animation: floatAround 20s infinite linear;
        }

        .floating-emoji:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-emoji:nth-child(2) { top: 20%; right: 15%; animation-delay: 3s; }
        .floating-emoji:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 6s; }
        .floating-emoji:nth-child(4) { bottom: 20%; right: 10%; animation-delay: 9s; }
        .floating-emoji:nth-child(5) { top: 50%; left: 5%; animation-delay: 12s; }
        .floating-emoji:nth-child(6) { top: 70%; right: 25%; animation-delay: 15s; }

        /* Main Container */
        .adventure-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(15px);
            padding: 3rem 2.5rem;
            max-width: 600px;
            width: 90%;
            position: relative;
            z-index: 10;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Header Section */
        .adventure-header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .adventure-illustration {
            margin-bottom: 2rem;
            position: relative;
            height: 100px;
        }

        .main-character {
            font-size: 4rem;
            animation: characterBounce 3s ease-in-out infinite;
        }

        .adventure-sparkles {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .sparkle {
            position: absolute;
            font-size: 1.5rem;
            animation: sparkleFloat 2s ease-in-out infinite;
        }

        .sparkle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
        .sparkle:nth-child(2) { top: 30%; right: 20%; animation-delay: 0.5s; }
        .sparkle:nth-child(3) { bottom: 30%; left: 30%; animation-delay: 1s; }
        .sparkle:nth-child(4) { bottom: 20%; right: 30%; animation-delay: 1.5s; }

        .adventure-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .adventure-subtitle {
            color: #6c757d;
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }

        .date-badge {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #5d4037;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }

        /* Progress Steps */
        .progress-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            gap: 1rem;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
        }

        .step.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            transform: scale(1.1);
        }

        .step.completed {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
        }

        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }

        .step:last-child::after {
            display: none;
        }

        /* Form Styling */
        .adventure-form {
            position: relative;
        }

        .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .label-emoji {
            font-size: 1.3rem;
        }

        .input-container {
            position: relative;
        }

        .form-control {
            border-radius: 20px;
            border: 3px solid #e9ecef;
            padding: 1.2rem 1.5rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.3rem rgba(102, 126, 234, 0.15);
            background: white;
            transform: translateY(-2px);
        }

        .form-control.is-valid {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.05);
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            background: rgba(220, 53, 69, 0.05);
        }

        .input-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .form-control:focus + .input-icon {
            color: #667eea;
            transform: translateY(-50%) scale(1.1);
        }

        .validation-feedback {
            display: block;
            margin-top: 0.8rem;
            font-size: 0.95rem;
            font-weight: 500;
        }

        /* Adventure Button */
        .btn-adventure {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            border-radius: 20px;
            padding: 1.2rem 2rem;
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
            width: 100%;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-adventure:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
            color: white;
        }

        .btn-adventure:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }

        .btn-adventure::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-adventure:hover::before {
            left: 100%;
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            position: relative;
            z-index: 2;
        }

        .btn-emoji {
            font-size: 1.5rem;
            animation: buttonPulse 2s infinite;
        }

        /* Info Cards */
        .info-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 5px solid #2196f3;
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        .info-header {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 1rem;
        }

        .info-icon {
            font-size: 1.8rem;
        }

        .info-title {
            font-weight: 700;
            color: #1565c0;
            font-size: 1.2rem;
        }

        .info-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .info-step {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            color: #1565c0;
            font-weight: 500;
        }

        .step-number {
            background: #2196f3;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        /* Back Link */
        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            z-index: 100;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        .back-link:hover {
            color: white;
            transform: translateX(-5px);
            background: rgba(255, 255, 255, 0.3);
        }

        /* Security Badge */
        .security-badge {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(40, 167, 69, 0.2);
        }

        .security-text {
            color: #155724;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Animations */
        @keyframes floatAround {
            0% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(90deg); }
            50% { transform: translateY(0px) rotate(180deg); }
            75% { transform: translateY(-10px) rotate(270deg); }
            100% { transform: translateY(0px) rotate(360deg); }
        }

        @keyframes characterBounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        @keyframes sparkleFloat {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
            50% { transform: translateY(-10px) scale(1.2); opacity: 1; }
        }

        @keyframes buttonPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes shimmer {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .adventure-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .adventure-title {
                font-size: 2rem;
            }

            .progress-steps {
                gap: 0.5rem;
            }

            .step {
                width: 35px;
                height: 35px;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Background Elements -->
    <div class="bg-elements">
        <div class="floating-emoji">🏠</div>
        <div class="floating-emoji">📚</div>
        <div class="floating-emoji">🎓</div>
        <div class="floating-emoji">⭐</div>
        <div class="floating-emoji">🎯</div>
        <div class="floating-emoji">🚀</div>
    </div>

    <a href="{{ url_for('index') }}" class="back-link">
        <i class="fas fa-arrow-left me-2"></i>Back to Adventure Hub
    </a>

    <div class="adventure-container">
        <!-- Header Section -->
        <div class="adventure-header">
            <div class="adventure-illustration">
                <div class="main-character">🎮</div>
                <div class="adventure-sparkles">
                    <div class="sparkle">✨</div>
                    <div class="sparkle">⭐</div>
                    <div class="sparkle">🌟</div>
                    <div class="sparkle">💫</div>
                </div>
            </div>
            <h1 class="adventure-title">Daily Check-In Quest</h1>
            <p class="adventure-subtitle">{{ time_greeting }}! Ready to join today's epic adventure?</p>
            <div class="date-badge">{{ today_date }} • {{ current_time }}</div>
        </div>

        <!-- Progress Steps -->
        <div class="progress-steps">
            <div class="step active" id="step1">1</div>
            <div class="step" id="step2">2</div>
            <div class="step" id="step3">🎉</div>
        </div>

        <!-- Info Card -->
        <div class="info-card">
            <div class="info-header">
                <div class="info-icon">🎯</div>
                <div class="info-title">Your Mission, Should You Choose to Accept:</div>
            </div>
            <ul class="info-steps">
                <li class="info-step">
                    <div class="step-number">1</div>
                    <span>Enter your magical email address (your hostel ID)</span>
                </li>
                <li class="info-step">
                    <div class="step-number">2</div>
                    <span>Confirm it by typing the same email again (security spell)</span>
                </li>
                <li class="info-step">
                    <div class="step-number">3</div>
                    <span>Hit the adventure button and become today's hero!</span>
                </li>
            </ul>
        </div>

        <div id="alertContainer"></div>

        <!-- Adventure Form -->
        <form id="attendanceForm" class="adventure-form">
            <div class="form-group">
                <label for="username" class="form-label">
                    <span class="label-emoji">🧙‍♂️</span>
                    <span>Your Magical Email Address</span>
                </label>
                <div class="input-container">
                    <input type="email" class="form-control" id="username" name="username"
                           placeholder="<EMAIL>" required>
                    <div class="input-icon">📧</div>
                </div>
                <div class="invalid-feedback"></div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <span class="label-emoji">🔐</span>
                    <span>Confirm Your Magical Email</span>
                </label>
                <div class="input-container">
                    <input type="email" class="form-control" id="password" name="password"
                           placeholder="<EMAIL>" required>
                    <div class="input-icon">🔑</div>
                </div>
                <div class="invalid-feedback"></div>
            </div>

            <button type="submit" class="btn btn-adventure" id="submitBtn">
                <div class="btn-content">
                    <span class="btn-emoji" id="submitEmoji">🚀</span>
                    <span id="submitText">Start Your Adventure</span>
                    <span id="submitSpinner" class="d-none">
                        <span class="spinner-border spinner-border-sm" role="status"></span>
                        <span>Casting Spell...</span>
                    </span>
                </div>
            </button>
        </form>

        <!-- Security Badge -->
        <div class="security-badge">
            <div class="security-text">
                <span>🛡️</span>
                <span>Protected by Hostel Magic & Advanced Security Spells</span>
                <span>✨</span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Progress tracking
        let currentStep = 1;

        // Update progress steps
        function updateProgress(step) {
            currentStep = step;
            document.querySelectorAll('.step').forEach((stepEl, index) => {
                stepEl.classList.remove('active', 'completed');
                if (index + 1 < step) {
                    stepEl.classList.add('completed');
                } else if (index + 1 === step) {
                    stepEl.classList.add('active');
                }
            });
        }

        // Confetti animation
        function createConfetti() {
            const confettiContainer = document.createElement('div');
            confettiContainer.style.position = 'fixed';
            confettiContainer.style.top = '0';
            confettiContainer.style.left = '0';
            confettiContainer.style.width = '100%';
            confettiContainer.style.height = '100%';
            confettiContainer.style.pointerEvents = 'none';
            confettiContainer.style.zIndex = '9999';
            document.body.appendChild(confettiContainer);

            const emojis = ['🎉', '🎊', '⭐', '🌟', '✨', '🏆', '🎯', '🚀'];

            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.textContent = emojis[Math.floor(Math.random() * emojis.length)];
                confetti.style.position = 'absolute';
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.top = '-50px';
                confetti.style.fontSize = Math.random() * 20 + 15 + 'px';
                confetti.style.animation = `confettiFall ${Math.random() * 3 + 2}s linear forwards`;
                confettiContainer.appendChild(confetti);
            }

            // Remove confetti after animation
            setTimeout(() => {
                document.body.removeChild(confettiContainer);
            }, 5000);
        }

        // Add confetti animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confettiFall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Form submission
        document.getElementById('attendanceForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');
            const submitEmoji = document.getElementById('submitEmoji');
            const alertContainer = document.getElementById('alertContainer');

            // Clear previous alerts
            alertContainer.innerHTML = '';

            // Clear validation states
            document.querySelectorAll('.form-control').forEach(input => {
                input.classList.remove('is-invalid', 'is-valid');
            });

            // Basic validation
            if (!username || !password) {
                showAlert('🚨 Oops! Both magical fields need your email spell!', 'danger');
                return;
            }

            // Email format validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(username)) {
                document.getElementById('username').classList.add('is-invalid');
                document.querySelector('#username ~ .invalid-feedback').textContent = 'This doesn\'t look like a magical email address! 🧙‍♂️';
                showAlert('🔮 Please enter a valid magical email address!', 'danger');
                return;
            }

            if (!emailRegex.test(password)) {
                document.getElementById('password').classList.add('is-invalid');
                document.querySelector('#password ~ .invalid-feedback').textContent = 'This doesn\'t look like a magical email address! 🧙‍♂️';
                showAlert('🔮 Please enter a valid magical email address!', 'danger');
                return;
            }

            // Check if username and password match
            if (username.toLowerCase() !== password.toLowerCase()) {
                document.getElementById('password').classList.add('is-invalid');
                document.querySelector('#password ~ .invalid-feedback').textContent = 'The magic spells don\'t match! Both must be identical! ⚡';
                showAlert('🔐 Both magical email spells must be identical to unlock your adventure!', 'danger');
                return;
            }

            // Mark fields as valid and update progress
            document.getElementById('username').classList.add('is-valid');
            document.getElementById('password').classList.add('is-valid');
            updateProgress(2);

            // Show loading state with magical animation
            submitBtn.disabled = true;
            submitText.classList.add('d-none');
            submitSpinner.classList.remove('d-none');
            submitEmoji.textContent = '🔮';

            // Submit form
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);

            fetch('/mark_attendance_email', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Success! Update to final step
                    updateProgress(3);

                    // Create confetti celebration
                    createConfetti();

                    // Show success message with celebration and timestamp
                    let successMessage = `🎉 QUEST COMPLETED! ${data.message}`;
                    if (data.check_in_time) {
                        successMessage += ` ⏰ Checked in at ${data.check_in_time}!`;
                    }
                    showAlert(successMessage, 'success');

                    // Show achievements if any
                    if (data.achievements && data.achievements.length > 0) {
                        setTimeout(() => {
                            let achievementMessage = '🏆 ACHIEVEMENTS UNLOCKED! ';
                            data.achievements.forEach(achievement => {
                                achievementMessage += `${achievement.emoji} ${achievement.title}: ${achievement.description} `;
                            });
                            showAlert(achievementMessage, 'info');
                        }, 1000);
                    }

                    // Show additional hero info with timestamp
                    if (data.student_name) {
                        setTimeout(() => {
                            let heroMessage = `🏆 Welcome back, Hero ${data.student_name}! 🏠 Room: ${data.room_no} | 🎓 Branch: ${data.branch} | 🔥 Epic Streak: ${data.streak} days!`;
                            if (data.check_in_time_full) {
                                heroMessage += ` ⏰ Checked in: ${data.check_in_time_full}`;
                            }
                            showAlert(heroMessage, 'info');
                        }, 2000);
                    }

                    // Change button to celebration mode
                    submitEmoji.textContent = '🏆';
                    submitText.textContent = 'Adventure Complete!';
                    submitText.classList.remove('d-none');
                    submitSpinner.classList.add('d-none');

                    // Reset form after celebration
                    setTimeout(() => {
                        document.getElementById('attendanceForm').reset();
                        document.querySelectorAll('.form-control').forEach(input => {
                            input.classList.remove('is-valid');
                        });
                        updateProgress(1);
                        submitEmoji.textContent = '🚀';
                        submitText.textContent = 'Start Your Adventure';
                        submitBtn.disabled = false;
                    }, 7000); // Extended time to show all messages
                } else {
                    showAlert(`⚠️ Quest Failed: ${data.message}`, 'danger');
                    submitEmoji.textContent = '😅';
                    submitText.textContent = 'Try Again, Hero!';
                    submitText.classList.remove('d-none');
                    submitSpinner.classList.add('d-none');
                    submitBtn.disabled = false;

                    // Reset button after a moment
                    setTimeout(() => {
                        submitEmoji.textContent = '🚀';
                        submitText.textContent = 'Start Your Adventure';
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('🌐 Network magic failed! Check your connection and try again, brave adventurer!', 'danger');
                submitEmoji.textContent = '📡';
                submitText.textContent = 'Connection Lost';
                submitText.classList.remove('d-none');
                submitSpinner.classList.add('d-none');
                submitBtn.disabled = false;

                // Reset button after a moment
                setTimeout(() => {
                    submitEmoji.textContent = '🚀';
                    submitText.textContent = 'Start Your Adventure';
                }, 3000);
            });
        });

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');

            // Enhanced alert styling
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'info' ? 'alert-info' : 'alert-danger';

            alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
            alertDiv.style.borderRadius = '20px';
            alertDiv.style.border = 'none';
            alertDiv.style.fontWeight = '600';
            alertDiv.style.fontSize = '1.1rem';
            alertDiv.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';

            alertDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    ${message}
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alertDiv);

            // Add entrance animation
            alertDiv.style.transform = 'translateY(-20px)';
            alertDiv.style.opacity = '0';
            setTimeout(() => {
                alertDiv.style.transition = 'all 0.3s ease';
                alertDiv.style.transform = 'translateY(0)';
                alertDiv.style.opacity = '1';
            }, 10);

            // Auto-dismiss with celebration for success
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.style.transform = 'translateY(-20px)';
                        alertDiv.style.opacity = '0';
                        setTimeout(() => alertDiv.remove(), 300);
                    }
                }, 7000);
            }
        }

        // Real-time magical validation feedback
        document.getElementById('password').addEventListener('input', function() {
            const username = document.getElementById('username').value.trim().toLowerCase();
            const password = this.value.trim().toLowerCase();

            if (password && username && password !== username) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
                document.querySelector('#password ~ .invalid-feedback').textContent = '⚡ The magic spells don\'t match! Make them identical!';
            } else if (password && username && password === username) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
                document.querySelector('#password ~ .invalid-feedback').textContent = '';
            }
        });

        document.getElementById('username').addEventListener('input', function() {
            const password = document.getElementById('password').value.trim().toLowerCase();
            const username = this.value.trim().toLowerCase();

            // Update progress when first field is filled
            if (username && currentStep === 1) {
                updateProgress(2);
            } else if (!username && currentStep === 2) {
                updateProgress(1);
            }

            if (password && username && password !== username) {
                document.getElementById('password').classList.add('is-invalid');
                document.getElementById('password').classList.remove('is-valid');
                document.querySelector('#password ~ .invalid-feedback').textContent = '⚡ The magic spells don\'t match! Make them identical!';
            } else if (password && username && password === username) {
                document.getElementById('password').classList.remove('is-invalid');
                document.getElementById('password').classList.add('is-valid');
                document.querySelector('#password ~ .invalid-feedback').textContent = '';
            }
        });

        // Add magical focus effects
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Auto-focus first input
        document.getElementById('username').focus();
    </script>
</body>
</html>
