from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, make_response
import random
from datetime import date, datetime
from models import DatabaseManager
from pdf_generator import AttendancePDFGenerator
from config import Config
from email_auth import initialize_email_auth, get_email_auth_manager
from timezone_config import get_current_timestamp, format_timestamp, get_time_greeting

app = Flask(__name__)
app.secret_key = Config.SECRET_KEY
app.config.from_object(Config)

# Initialize managers
db_manager = DatabaseManager()
pdf_generator = AttendancePDFGenerator()

# Initialize email authentication
email_auth = initialize_email_auth(db_manager)

@app.route('/')
def index():
    """Main dashboard with time-aware features"""
    # Get current time and greeting
    current_time = get_current_timestamp()
    time_greeting = get_time_greeting(current_time)

    # Get time-aware welcome messages
    welcome_messages = [
        f"{time_greeting}! Welcome to your Hostel Adventure Hub! 🏠✨",
        f"{time_greeting}, Heroes! Ready for today's attendance quest? 🎮",
        f"{time_greeting}! Time to check in and join the squad! 🚀",
        f"{time_greeting}, Legends! Your daily adventure awaits! 🌟"
    ]
    welcome_msg = random.choice(welcome_messages)

    # Get total students count
    students = db_manager.get_all_students()
    total_students = len(students)

    # Get today's attendance with timestamps
    today_attendance = db_manager.get_today_attendance()
    attendance_count = len(today_attendance)
    attendance_percentage = round((attendance_count / total_students * 100) if total_students > 0 else 0, 1)

    # Get authentication stats if available
    auth_stats = {}
    if email_auth:
        auth_stats = email_auth.get_auth_stats()

    return render_template('index.html',
                         welcome_message=welcome_msg,
                         total_students=total_students,
                         today_attendance=today_attendance,
                         attendance_count=attendance_count,
                         attendance_percentage=attendance_percentage,
                         auth_stats=auth_stats,
                         today_date=format_timestamp(current_time, 'date'),
                         current_time=format_timestamp(current_time, 'time'),
                         time_greeting=time_greeting)

@app.route('/admin')
def admin():
    """Admin panel for user management"""
    students = db_manager.get_all_students()
    return render_template('admin.html', students=students)

@app.route('/add_student', methods=['POST'])
def add_student():
    """Add a new student"""
    try:
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip().lower()
        room_no = request.form.get('room_no', '').strip()
        branch = request.form.get('branch', '').strip()
        year = int(request.form.get('year', 0))
        
        # Validation
        if not all([name, email, room_no, branch, year]):
            flash('All fields are required! 📝', 'error')
            return redirect(url_for('admin'))
        
        if year < 1 or year > 4:
            flash('Year must be between 1 and 4! 📚', 'error')
            return redirect(url_for('admin'))
        
        # Add student
        student_id = db_manager.add_student(name, email, room_no, branch, year)
        
        if student_id:
            flash(f'🎉 Welcome {name}! Student added successfully! ✨', 'success')
        else:
            flash('😅 Oops! Email already exists. Try a different one!', 'error')
            
    except ValueError:
        flash('Invalid year format! Please enter a number 🔢', 'error')
    except Exception as e:
        flash(f'Something went wrong! 😅 {str(e)}', 'error')
    
    return redirect(url_for('admin'))

@app.route('/delete_student/<int:student_id>', methods=['POST'])
def delete_student(student_id):
    """Delete a student"""
    try:
        if db_manager.delete_student(student_id):
            flash('👋 Student removed successfully! Hope they had fun! 🎈', 'success')
        else:
            flash('🤔 Student not found or already removed!', 'error')
    except Exception as e:
        flash(f'Error removing student! 😅 {str(e)}', 'error')
    
    return redirect(url_for('admin'))

@app.route('/attendance')
def attendance_page():
    """Email-based attendance page"""
    return render_template('attendance.html')

@app.route('/login_attendance')
def login_attendance():
    """Email login attendance page with time information"""
    current_time = get_current_timestamp()
    time_greeting = get_time_greeting(current_time)

    return render_template('login_attendance.html',
                         today_date=format_timestamp(current_time, 'date'),
                         current_time=format_timestamp(current_time, 'time'),
                         time_greeting=time_greeting)

@app.route('/mark_attendance_email', methods=['POST'])
def mark_attendance_email():
    """Handle attendance marking using email authentication"""
    try:
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()
        client_ip = request.environ.get('REMOTE_ADDR', 'unknown')

        if not email_auth:
            return jsonify({
                'success': False,
                'message': 'Authentication system not available! 😅'
            })

        # Authenticate student
        auth_success, auth_message, student_data = email_auth.authenticate_student(
            username, password, client_ip
        )

        if not auth_success:
            return jsonify({
                'success': False,
                'message': auth_message
            })

        # Mark attendance with timestamp and achievements
        attendance_success, attendance_message, additional_data = email_auth.mark_attendance(
            student_data, client_ip
        )

        if attendance_success:
            # Prepare response with timestamp and achievement information
            response_data = {
                'success': True,
                'message': attendance_message,
                'student_name': student_data['name'],
                'room_no': student_data['room_no'],
                'branch': student_data['branch'],
                'year': student_data['year'],
                'streak': additional_data.get('streak', 0)
            }

            # Add timestamp information
            if additional_data:
                response_data.update({
                    'check_in_time': additional_data.get('check_in_time_display'),
                    'check_in_time_full': additional_data.get('check_in_time_formatted'),
                    'achievements': additional_data.get('achievements', []),
                    'greeting': additional_data.get('greeting'),
                    'timestamp': additional_data.get('check_in_timestamp').isoformat() if additional_data.get('check_in_timestamp') else None
                })

            return jsonify(response_data)
        else:
            return jsonify({
                'success': False,
                'message': attendance_message
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'System error occurred! Please try again. 😅'
        })

# Legacy route removed - replaced with email authentication

@app.route('/success')
def success_page():
    """Success page after check-in"""
    return render_template('success.html')

# QR code routes removed - replaced with email authentication

@app.route('/api/auth_stats')
def auth_stats_api():
    """API endpoint to get authentication statistics"""
    try:
        if email_auth:
            stats = email_auth.get_auth_stats()
            return jsonify({
                'success': True,
                'stats': stats
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Authentication system not available'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/admin/reset_attendance', methods=['POST'])
def reset_today_attendance():
    """Reset all attendance for today (Admin function)"""
    try:
        count = db_manager.reset_today_attendance()
        if count > 0:
            flash(f'🗑️ Successfully reset today\'s attendance! Removed {count} records. 📊', 'success')
        else:
            flash('ℹ️ No attendance records found for today to reset. 📅', 'info')
    except Exception as e:
        flash(f'❌ Error resetting attendance: {str(e)}', 'error')

    return redirect(url_for('index'))

@app.route('/admin/remove_attendance/<int:student_id>', methods=['POST'])
def remove_student_attendance(student_id):
    """Remove specific student's attendance for today (Admin function)"""
    try:
        student_name = db_manager.remove_student_attendance_today(student_id)
        if student_name:
            flash(f'✅ Removed {student_name} from today\'s attendance! 👋', 'success')
        else:
            flash('⚠️ Student attendance record not found for today! 🔍', 'warning')
    except Exception as e:
        flash(f'❌ Error removing attendance: {str(e)}', 'error')

    return redirect(url_for('index'))

@app.route('/admin/export_attendance_pdf')
def export_attendance_pdf():
    """Export today's attendance to PDF (Admin function)"""
    try:
        # Get today's attendance data
        today_attendance = db_manager.get_today_attendance()
        total_students = len(db_manager.get_all_students())
        attendance_count = len(today_attendance)
        attendance_percentage = round((attendance_count / total_students * 100) if total_students > 0 else 0, 1)

        if attendance_count == 0:
            flash('⚠️ No attendance records found for today! Cannot generate PDF. 📄', 'warning')
            return redirect(url_for('index'))

        # Generate PDF
        pdf_data = pdf_generator.generate_attendance_pdf(
            attendance_data=today_attendance,
            total_students=total_students,
            attendance_count=attendance_count,
            attendance_percentage=attendance_percentage,
            report_date=date.today()
        )

        # Create response with PDF
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="attendance_report_{date.today().strftime("%Y-%m-%d")}.pdf"'

        # Flash success message for next page load
        flash(f'📄 PDF report generated successfully! Downloaded attendance report for {date.today().strftime("%B %d, %Y")} 🎉', 'success')

        return response

    except Exception as e:
        flash(f'❌ Error generating PDF report: {str(e)} 📄', 'error')
        return redirect(url_for('index'))

# Google Forms Integration Routes (Temporarily Disabled)
# TODO: Re-enable Google Forms integration

@app.route('/admin/google_forms')
def google_forms_admin():
    """Google Forms integration admin panel (disabled)"""
    flash('🚧 Google Forms integration is temporarily disabled. Email authentication is active! 📧', 'info')
    return redirect(url_for('admin'))

if __name__ == '__main__':
    print("🚀 Starting Hostel Attendance System...")
    print("📧 Email-based authentication enabled!")
    print("🎉 Ready to track those hostel vibes!")
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
