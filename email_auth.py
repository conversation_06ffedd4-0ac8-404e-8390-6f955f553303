#!/usr/bin/env python3
"""
Email-Based Authentication System for HOSTEL-ATTENDANCE
Simple authentication where students use their email as both username and password
"""

import re
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
from config import Config
from timezone_config import (
    get_current_timestamp,
    format_timestamp,
    get_time_based_achievement,
    get_time_greeting
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailAuthManager:
    """
    Manages email-based authentication for attendance marking
    """
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        
        # Rate limiting: track attempts per IP/email
        self.attempt_tracker = defaultdict(list)
        self.max_attempts_per_hour = 10
        self.max_attempts_per_minute = 3
        
        # Audit logging
        self.audit_log_file = 'email_auth_audit.json'
        
        logger.info("🔐 Email Authentication Manager initialized")
    
    def validate_email_format(self, email: str) -> bool:
        """
        Validate email format using regex
        
        Args:
            email: Email address to validate
            
        Returns:
            bool: True if valid email format, False otherwise
        """
        if not email or not isinstance(email, str):
            return False
        
        # Basic email regex pattern
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, email.strip()) is not None
    
    def check_rate_limit(self, identifier: str) -> Tuple[bool, str]:
        """
        Check if the identifier (IP or email) has exceeded rate limits
        
        Args:
            identifier: IP address or email to check
            
        Returns:
            Tuple of (is_allowed, error_message)
        """
        now = datetime.now()
        
        # Clean old attempts (older than 1 hour)
        cutoff_time = now - timedelta(hours=1)
        self.attempt_tracker[identifier] = [
            attempt_time for attempt_time in self.attempt_tracker[identifier]
            if attempt_time > cutoff_time
        ]
        
        attempts = self.attempt_tracker[identifier]
        
        # Check hourly limit
        if len(attempts) >= self.max_attempts_per_hour:
            return False, f"Too many attempts. Please try again in an hour. 🕐"
        
        # Check per-minute limit
        minute_cutoff = now - timedelta(minutes=1)
        recent_attempts = [
            attempt_time for attempt_time in attempts
            if attempt_time > minute_cutoff
        ]
        
        if len(recent_attempts) >= self.max_attempts_per_minute:
            return False, f"Too many attempts. Please wait a minute before trying again. ⏰"
        
        return True, ""
    
    def record_attempt(self, identifier: str):
        """
        Record an authentication attempt
        
        Args:
            identifier: IP address or email that made the attempt
        """
        self.attempt_tracker[identifier].append(datetime.now())
    
    def authenticate_student(self, username: str, password: str, client_ip: str = "unknown") -> Tuple[bool, str, Optional[Dict]]:
        """
        Authenticate student using email-based authentication
        
        Args:
            username: Email address used as username
            password: Email address used as password (should match username)
            client_ip: Client IP address for rate limiting
            
        Returns:
            Tuple of (success, message, student_data)
        """
        try:
            # Normalize inputs
            username = username.strip().lower() if username else ""
            password = password.strip().lower() if password else ""
            
            # Basic validation
            if not username or not password:
                self.log_auth_attempt(username, client_ip, False, "Empty credentials")
                return False, "Please enter both username and password! 📝", None
            
            # Check rate limiting
            rate_allowed, rate_message = self.check_rate_limit(client_ip)
            if not rate_allowed:
                self.log_auth_attempt(username, client_ip, False, f"Rate limited: {rate_message}")
                return False, rate_message, None
            
            # Record attempt for rate limiting
            self.record_attempt(client_ip)
            self.record_attempt(username)  # Also track per email
            
            # Validate email format
            if not self.validate_email_format(username):
                self.log_auth_attempt(username, client_ip, False, "Invalid email format")
                return False, "Please enter a valid email address! 📧", None
            
            # Check if username and password match (both should be the same email)
            if username != password:
                self.log_auth_attempt(username, client_ip, False, "Username/password mismatch")
                return False, "Username and password must be the same email address! 🔐", None
            
            # Check if student exists in database
            if not self.db_manager:
                self.log_auth_attempt(username, client_ip, False, "Database not available")
                return False, "System error: Database not available! 😅", None
            
            student = self.db_manager.get_student_by_email(username)
            if not student:
                self.log_auth_attempt(username, client_ip, False, "Student not found")
                return False, "Email not found! Are you registered? 🔍", None
            
            # Check if already present today
            if self.db_manager.is_present_today(student['id']):
                self.log_auth_attempt(username, client_ip, False, "Already present today")
                return False, f"Hey {student['name']}! 👋 You're already checked in today! 🎯", None
            
            # Authentication successful
            self.log_auth_attempt(username, client_ip, True, "Authentication successful")
            logger.info(f"✅ Authentication successful for {student['name']} ({username})")
            
            return True, "Authentication successful! 🎉", student
            
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            self.log_auth_attempt(username, client_ip, False, f"System error: {str(e)}")
            return False, "System error occurred! Please try again. 😅", None
    
    def mark_attendance(self, student_data: Dict, client_ip: str = "unknown") -> Tuple[bool, str, Optional[Dict]]:
        """
        Mark attendance for authenticated student with timestamp and achievements

        Args:
            student_data: Student information from database
            client_ip: Client IP address for logging

        Returns:
            Tuple of (success, message, additional_data)
        """
        try:
            if not self.db_manager:
                return False, "System error: Database not available! 😅", None

            # Get current timestamp
            check_in_timestamp = get_current_timestamp()

            # Mark attendance with timestamp
            success, timestamp = self.db_manager.mark_attendance(student_data['id'], check_in_timestamp=check_in_timestamp)

            if success:
                # Get attendance streak
                streak = self.db_manager.get_attendance_streak(student_data['id'])

                # Get time-based achievements
                achievements = get_time_based_achievement(check_in_timestamp)

                # Get time greeting
                greeting = get_time_greeting(check_in_timestamp)

                # Create success message with time
                check_in_time_formatted = format_timestamp(check_in_timestamp, 'celebration')

                success_messages = [
                    f"{greeting}, {student_data['name']}! 🎉 Checked in at {check_in_time_formatted}! ✨",
                    f"Great to see you, {student_data['name']}! 👋 Attendance recorded at {check_in_time_formatted}! 🎯",
                    f"Hello {student_data['name']}! 😊 Successfully checked in at {check_in_time_formatted}! 📝",
                    f"Hi {student_data['name']}! 🌟 You're all set! Checked in at {check_in_time_formatted}! ✅"
                ]

                import random
                message = random.choice(success_messages)

                # Add streak message if applicable
                if streak > 1:
                    streak_messages = [
                        f"Amazing! {streak} days in a row! 🔥",
                        f"Fantastic streak of {streak} days! 🌟",
                        f"You're on fire! {streak} consecutive days! 🚀",
                        f"Incredible! {streak} days straight! 💪"
                    ]
                    message += f" {random.choice(streak_messages)}"

                # Add achievement messages
                achievement_message = ""
                if achievements:
                    for achievement in achievements:
                        achievement_message += f" {achievement['emoji']} {achievement['title']}: {achievement['description']}"

                # Prepare additional data for response
                additional_data = {
                    'check_in_timestamp': check_in_timestamp,
                    'check_in_time_formatted': format_timestamp(check_in_timestamp, 'full'),
                    'check_in_time_display': check_in_time_formatted,
                    'achievements': achievements,
                    'greeting': greeting,
                    'streak': streak
                }

                # Log successful attendance with timestamp
                self.log_attendance_event_with_timestamp(student_data, client_ip, True, message, check_in_timestamp, achievements)
                logger.info(f"✅ Attendance marked for {student_data['name']} at {check_in_time_formatted} (streak: {streak})")

                # Combine messages
                full_message = message + achievement_message

                return True, full_message, additional_data
            else:
                error_msg = "Failed to mark attendance! Please try again. 😅"
                self.log_attendance_event(student_data, client_ip, False, error_msg)
                return False, error_msg, None

        except Exception as e:
            logger.error(f"❌ Error marking attendance: {e}")
            error_msg = f"System error: {str(e)} 😅"
            self.log_attendance_event(student_data, client_ip, False, error_msg)
            return False, error_msg, None
    
    def log_auth_attempt(self, email: str, client_ip: str, success: bool, details: str):
        """
        Log authentication attempt for audit purposes
        
        Args:
            email: Email address used in attempt
            client_ip: Client IP address
            success: Whether attempt was successful
            details: Additional details about the attempt
        """
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'email': email,
                'client_ip': client_ip,
                'success': success,
                'details': details,
                'type': 'authentication'
            }
            
            self._append_to_audit_log(log_entry)
            
        except Exception as e:
            logger.error(f"❌ Error logging auth attempt: {e}")
    
    def log_attendance_event(self, student_data: Dict, client_ip: str, success: bool, message: str):
        """
        Log attendance marking event
        
        Args:
            student_data: Student information
            client_ip: Client IP address
            success: Whether attendance marking was successful
            message: Result message
        """
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'student_id': student_data.get('id'),
                'student_name': student_data.get('name'),
                'student_email': student_data.get('email'),
                'client_ip': client_ip,
                'success': success,
                'message': message,
                'type': 'attendance'
            }
            
            self._append_to_audit_log(log_entry)
            
        except Exception as e:
            logger.error(f"❌ Error logging attendance event: {e}")

    def log_attendance_event_with_timestamp(self, student_data: Dict, client_ip: str, success: bool, message: str, timestamp, achievements=None):
        """
        Log attendance marking event with timestamp and achievements

        Args:
            student_data: Student information
            client_ip: Client IP address
            success: Whether attendance marking was successful
            message: Result message
            timestamp: Check-in timestamp
            achievements: Time-based achievements earned
        """
        try:
            log_entry = {
                'timestamp': get_current_timestamp().isoformat(),
                'check_in_timestamp': timestamp.isoformat() if timestamp else None,
                'check_in_time_formatted': format_timestamp(timestamp, 'full') if timestamp else None,
                'student_id': student_data.get('id'),
                'student_name': student_data.get('name'),
                'student_email': student_data.get('email'),
                'client_ip': client_ip,
                'success': success,
                'message': message,
                'achievements': achievements,
                'type': 'attendance_with_timestamp'
            }

            self._append_to_audit_log(log_entry)

        except Exception as e:
            logger.error(f"❌ Error logging attendance event with timestamp: {e}")
    
    def _append_to_audit_log(self, log_entry: Dict):
        """
        Append entry to audit log file
        
        Args:
            log_entry: Log entry to append
        """
        try:
            # Load existing logs
            logs = []
            try:
                with open(self.audit_log_file, 'r') as f:
                    logs = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                logs = []
            
            # Add new entry
            logs.append(log_entry)
            
            # Keep only last 1000 entries to prevent file from growing too large
            if len(logs) > 1000:
                logs = logs[-1000:]
            
            # Save logs
            with open(self.audit_log_file, 'w') as f:
                json.dump(logs, f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Error writing to audit log: {e}")
    
    def get_auth_stats(self) -> Dict:
        """
        Get authentication statistics
        
        Returns:
            Dictionary with authentication statistics
        """
        try:
            # Load audit logs
            logs = []
            try:
                with open(self.audit_log_file, 'r') as f:
                    logs = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                logs = []
            
            # Calculate statistics
            total_attempts = len([log for log in logs if log.get('type') == 'authentication'])
            successful_auths = len([log for log in logs if log.get('type') == 'authentication' and log.get('success')])
            total_attendance = len([log for log in logs if log.get('type') == 'attendance' and log.get('success')])
            
            # Recent activity (last 24 hours)
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_logs = [
                log for log in logs 
                if datetime.fromisoformat(log['timestamp']) > cutoff_time
            ]
            recent_attempts = len([log for log in recent_logs if log.get('type') == 'authentication'])
            recent_attendance = len([log for log in recent_logs if log.get('type') == 'attendance' and log.get('success')])
            
            return {
                'total_auth_attempts': total_attempts,
                'successful_authentications': successful_auths,
                'total_attendance_marked': total_attendance,
                'recent_attempts_24h': recent_attempts,
                'recent_attendance_24h': recent_attendance,
                'success_rate': round((successful_auths / total_attempts * 100) if total_attempts > 0 else 0, 1),
                'active_rate_limits': len(self.attempt_tracker)
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting auth stats: {e}")
            return {}

# Global instance
email_auth_manager = None

def initialize_email_auth(db_manager):
    """Initialize the email authentication manager"""
    global email_auth_manager
    
    try:
        email_auth_manager = EmailAuthManager(db_manager)
        logger.info("🎉 Email Authentication Manager initialized successfully")
        return email_auth_manager
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Email Authentication Manager: {e}")
        return None

def get_email_auth_manager():
    """Get the global email authentication manager instance"""
    return email_auth_manager
