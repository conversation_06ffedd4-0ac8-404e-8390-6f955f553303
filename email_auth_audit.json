[{"timestamp": "2025-06-05T21:43:55.136654", "email": "<EMAIL>", "client_ip": "127.0.0.1", "success": true, "details": "Authentication successful", "type": "authentication"}, {"timestamp": "2025-06-05T21:43:55.159783", "student_id": 6, "student_name": "Admin User", "student_email": "<EMAIL>", "client_ip": "127.0.0.1", "success": true, "message": "Welcome Admin User! 🎉 Attendance marked successfully! ✨", "type": "attendance"}, {"timestamp": "2025-06-05T22:00:24.149161", "email": "<EMAIL>", "client_ip": "127.0.0.1", "success": true, "details": "Authentication successful", "type": "authentication"}, {"timestamp": "2025-06-05T22:00:24.186038", "student_id": 7, "student_name": "asha", "student_email": "<EMAIL>", "client_ip": "127.0.0.1", "success": true, "message": "Great to see you asha! 👋 You're all set! 🎯", "type": "attendance"}, {"timestamp": "2025-06-05T22:01:08.654011", "email": "<EMAIL>", "client_ip": "127.0.0.1", "success": false, "details": "Already present today", "type": "authentication"}, {"timestamp": "2025-06-05T22:01:12.161179", "email": "<EMAIL>", "client_ip": "127.0.0.1", "success": false, "details": "Already present today", "type": "authentication"}, {"timestamp": "2025-06-05T22:20:52.985869", "email": "<EMAIL>", "client_ip": "127.0.0.1", "success": true, "details": "Authentication successful", "type": "authentication"}, {"timestamp": "2025-06-05T22:20:53.030160+05:30", "check_in_timestamp": "2025-06-05T22:20:52.991462+05:30", "check_in_time_formatted": "June 05, 2025 at 10:20 PM", "student_id": 6, "student_name": "Admin User", "student_email": "<EMAIL>", "client_ip": "127.0.0.1", "success": true, "message": "🌙 Good Night, Admin User! 🎉 Checked in at 10:20 PM! ✨", "achievements": [{"emoji": "🦉", "title": "Night Owl", "description": "Late night check-in! You're dedicated!", "type": "night_owl", "points": 5}], "type": "attendance_with_timestamp"}]