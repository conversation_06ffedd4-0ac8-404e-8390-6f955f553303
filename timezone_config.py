"""
🕐 Timezone Configuration Module for Hostel Attendance System
Handles all timezone-related operations and timestamp formatting
"""

import pytz
from datetime import datetime, time
from dateutil import tz
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TimezoneManager:
    """Manages timezone operations for the attendance system"""
    
    def __init__(self, timezone_name='Asia/Kolkata'):
        """
        Initialize timezone manager
        
        Args:
            timezone_name (str): Timezone name (default: Asia/Kolkata for IST)
        """
        self.timezone_name = timezone_name
        self.timezone = pytz.timezone(timezone_name)
        self.utc = pytz.UTC
        
        logger.info(f"🕐 Timezone Manager initialized with {timezone_name}")
    
    def get_current_time(self):
        """
        Get current time in the configured timezone
        
        Returns:
            datetime: Current datetime in configured timezone
        """
        utc_now = datetime.now(self.utc)
        local_time = utc_now.astimezone(self.timezone)
        return local_time
    
    def get_current_timestamp(self):
        """
        Get current timestamp for database storage
        
        Returns:
            datetime: Current datetime with timezone info
        """
        return self.get_current_time()
    
    def format_timestamp(self, timestamp, format_type='full'):
        """
        Format timestamp for display
        
        Args:
            timestamp (datetime): Timestamp to format
            format_type (str): Type of formatting ('full', 'time', 'date', 'short')
        
        Returns:
            str: Formatted timestamp string
        """
        if not timestamp:
            return "Not available"
        
        # Ensure timestamp has timezone info
        if timestamp.tzinfo is None:
            timestamp = self.timezone.localize(timestamp)
        elif timestamp.tzinfo != self.timezone:
            timestamp = timestamp.astimezone(self.timezone)
        
        formats = {
            'full': '%B %d, %Y at %I:%M %p',  # June 5, 2025 at 9:45 AM
            'time': '%I:%M %p',               # 9:45 AM
            'date': '%B %d, %Y',              # June 5, 2025
            'short': '%m/%d/%Y %I:%M %p',     # 06/05/2025 9:45 AM
            'admin': '%Y-%m-%d %H:%M:%S',     # 2025-06-05 09:45:30
            'celebration': '%I:%M %p',        # 9:45 AM (for success messages)
        }
        
        format_string = formats.get(format_type, formats['full'])
        return timestamp.strftime(format_string)
    
    def get_time_based_achievement(self, timestamp):
        """
        Determine time-based achievement based on check-in time
        
        Args:
            timestamp (datetime): Check-in timestamp
        
        Returns:
            dict: Achievement information with emoji, title, and description
        """
        if not timestamp:
            return None
        
        # Ensure timestamp has timezone info
        if timestamp.tzinfo is None:
            timestamp = self.timezone.localize(timestamp)
        elif timestamp.tzinfo != self.timezone:
            timestamp = timestamp.astimezone(self.timezone)
        
        check_in_time = timestamp.time()
        
        # Define time-based achievements
        achievements = []
        
        # Early Bird (before 8:00 AM)
        if check_in_time < time(8, 0):
            achievements.append({
                'emoji': '🌅',
                'title': 'Early Bird Champion',
                'description': 'Checked in before 8:00 AM! You\'re a morning hero!',
                'type': 'early_bird',
                'points': 10
            })
        
        # Super Early Bird (before 6:00 AM)
        if check_in_time < time(6, 0):
            achievements.append({
                'emoji': '🦅',
                'title': 'Super Early Bird',
                'description': 'Incredible! Checked in before 6:00 AM! You\'re unstoppable!',
                'type': 'super_early_bird',
                'points': 20
            })
        
        # Night Owl (after 10:00 PM)
        if check_in_time > time(22, 0):
            achievements.append({
                'emoji': '🦉',
                'title': 'Night Owl',
                'description': 'Late night check-in! You\'re dedicated!',
                'type': 'night_owl',
                'points': 5
            })
        
        # Perfect Timing (between 9:00-10:00 AM)
        if time(9, 0) <= check_in_time <= time(10, 0):
            achievements.append({
                'emoji': '🎯',
                'title': 'Perfect Timing',
                'description': 'Checked in during the golden hour (9-10 AM)!',
                'type': 'perfect_timing',
                'points': 5
            })
        
        # Lunch Break Hero (12:00-1:00 PM)
        if time(12, 0) <= check_in_time <= time(13, 0):
            achievements.append({
                'emoji': '🍽️',
                'title': 'Lunch Break Hero',
                'description': 'Remembered to check in during lunch break!',
                'type': 'lunch_break',
                'points': 3
            })
        
        # Last Minute (after 11:00 PM)
        if check_in_time > time(23, 0):
            achievements.append({
                'emoji': '⏰',
                'title': 'Last Minute Warrior',
                'description': 'Just made it! Last minute check-in!',
                'type': 'last_minute',
                'points': 2
            })
        
        return achievements if achievements else None
    
    def get_time_greeting(self, timestamp=None):
        """
        Get time-appropriate greeting
        
        Args:
            timestamp (datetime): Timestamp to base greeting on (default: current time)
        
        Returns:
            str: Time-appropriate greeting with emoji
        """
        if not timestamp:
            timestamp = self.get_current_time()
        
        # Ensure timestamp has timezone info
        if timestamp.tzinfo is None:
            timestamp = self.timezone.localize(timestamp)
        elif timestamp.tzinfo != self.timezone:
            timestamp = timestamp.astimezone(self.timezone)
        
        hour = timestamp.hour
        
        if 5 <= hour < 12:
            return "🌅 Good Morning"
        elif 12 <= hour < 17:
            return "☀️ Good Afternoon"
        elif 17 <= hour < 21:
            return "🌆 Good Evening"
        else:
            return "🌙 Good Night"
    
    def is_same_day(self, timestamp1, timestamp2):
        """
        Check if two timestamps are on the same day
        
        Args:
            timestamp1 (datetime): First timestamp
            timestamp2 (datetime): Second timestamp
        
        Returns:
            bool: True if same day, False otherwise
        """
        if not timestamp1 or not timestamp2:
            return False
        
        # Convert to local timezone
        if timestamp1.tzinfo is None:
            timestamp1 = self.timezone.localize(timestamp1)
        else:
            timestamp1 = timestamp1.astimezone(self.timezone)
        
        if timestamp2.tzinfo is None:
            timestamp2 = self.timezone.localize(timestamp2)
        else:
            timestamp2 = timestamp2.astimezone(self.timezone)
        
        return timestamp1.date() == timestamp2.date()
    
    def get_day_start(self, date=None):
        """
        Get start of day (00:00:00) for given date
        
        Args:
            date (datetime or date): Date to get start of day for (default: today)
        
        Returns:
            datetime: Start of day timestamp
        """
        if date is None:
            date = self.get_current_time().date()
        elif hasattr(date, 'date'):
            date = date.date()
        
        start_of_day = datetime.combine(date, time.min)
        return self.timezone.localize(start_of_day)
    
    def get_day_end(self, date=None):
        """
        Get end of day (23:59:59) for given date
        
        Args:
            date (datetime or date): Date to get end of day for (default: today)
        
        Returns:
            datetime: End of day timestamp
        """
        if date is None:
            date = self.get_current_time().date()
        elif hasattr(date, 'date'):
            date = date.date()
        
        end_of_day = datetime.combine(date, time.max)
        return self.timezone.localize(end_of_day)

# Global timezone manager instance
timezone_manager = TimezoneManager('Asia/Kolkata')

# Convenience functions for easy access
def get_current_timestamp():
    """Get current timestamp in IST"""
    return timezone_manager.get_current_timestamp()

def format_timestamp(timestamp, format_type='full'):
    """Format timestamp for display"""
    return timezone_manager.format_timestamp(timestamp, format_type)

def get_time_based_achievement(timestamp):
    """Get time-based achievement for timestamp"""
    return timezone_manager.get_time_based_achievement(timestamp)

def get_time_greeting(timestamp=None):
    """Get time-appropriate greeting"""
    return timezone_manager.get_time_greeting(timestamp)

def is_same_day(timestamp1, timestamp2):
    """Check if timestamps are on same day"""
    return timezone_manager.is_same_day(timestamp1, timestamp2)

def get_day_start(date=None):
    """Get start of day timestamp"""
    return timezone_manager.get_day_start(date)

def get_day_end(date=None):
    """Get end of day timestamp"""
    return timezone_manager.get_day_end(date)
