#!/usr/bin/env python3
"""
PDF Generator for Hostel Attendance System
Generates professional PDF reports for daily attendance
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime, date
import io
import os
from timezone_config import format_timestamp, get_current_timestamp

class AttendancePDFGenerator:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom styles for the PDF"""
        # Title style
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        )
        
        # Subtitle style
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkgreen,
            fontName='Helvetica-Bold'
        )
        
        # Header style
        self.header_style = ParagraphStyle(
            'CustomHeader',
            parent=self.styles['Heading3'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_LEFT,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        )
        
        # Normal text style
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=TA_LEFT,
            fontName='Helvetica'
        )
        
        # Footer style
        self.footer_style = ParagraphStyle(
            'CustomFooter',
            parent=self.styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey,
            fontName='Helvetica-Oblique'
        )
    
    def generate_attendance_pdf(self, attendance_data, total_students, attendance_count, attendance_percentage, report_date=None):
        """Generate PDF report for daily attendance"""
        
        if report_date is None:
            report_date = date.today()
        
        # Create PDF buffer
        buffer = io.BytesIO()
        
        # Create PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        
        # Build PDF content
        story = []
        
        # Header section
        story.extend(self._build_header(report_date))
        
        # Summary statistics
        story.extend(self._build_summary(total_students, attendance_count, attendance_percentage))
        
        # Attendance table
        if attendance_data:
            story.extend(self._build_attendance_table(attendance_data))
        else:
            story.append(Paragraph("No attendance records found for this date.", self.normal_style))
            story.append(Spacer(1, 20))
        
        # Footer
        story.extend(self._build_footer())
        
        # Build PDF
        doc.build(story)
        
        # Get PDF data
        pdf_data = buffer.getvalue()
        buffer.close()
        
        return pdf_data
    
    def _build_header(self, report_date):
        """Build PDF header section"""
        story = []
        
        # Main title
        title = f"Daily Attendance Report"
        story.append(Paragraph(title, self.title_style))
        
        # Hostel name and date
        hostel_name = "🏠 Hostel Attendance System ✨"
        story.append(Paragraph(hostel_name, self.subtitle_style))
        
        # Report date
        date_str = report_date.strftime('%B %d, %Y')
        story.append(Paragraph(f"Report Date: {date_str}", self.header_style))
        
        # Generation timestamp with timezone
        generation_time = format_timestamp(get_current_timestamp(), 'admin')
        story.append(Paragraph(f"Generated on: {generation_time} IST", self.normal_style))
        
        story.append(Spacer(1, 30))
        
        return story
    
    def _build_summary(self, total_students, attendance_count, attendance_percentage):
        """Build summary statistics section"""
        story = []
        
        story.append(Paragraph("📊 Attendance Summary", self.header_style))
        
        # Create summary table
        summary_data = [
            ['Metric', 'Value'],
            ['Total Registered Students', str(total_students)],
            ['Students Present Today', str(attendance_count)],
            ['Attendance Percentage', f'{attendance_percentage}%'],
            ['Students Absent', str(total_students - attendance_count)]
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 30))
        
        return story
    
    def _build_attendance_table(self, attendance_data):
        """Build detailed attendance table"""
        story = []
        
        story.append(Paragraph("👥 Detailed Attendance List", self.header_style))
        
        # Table headers
        table_data = [
            ['#', 'Student Name', 'Room No.', 'Branch', 'Year', 'Check-in Time']
        ]
        
        # Add student data
        for i, student in enumerate(attendance_data, 1):
            # Use formatted timestamp if available, otherwise fallback to raw time
            if student.get('check_in_formatted'):
                time_display = student.get('check_in_formatted')
            elif student.get('check_in_timestamp'):
                try:
                    # Parse timestamp and format for PDF
                    from dateutil import parser
                    timestamp = parser.parse(student.get('check_in_timestamp'))
                    time_display = format_timestamp(timestamp, 'celebration')
                except:
                    time_display = student.get('check_in_time', 'N/A')
            else:
                # Fallback to raw time formatting
                check_in_time = student.get('check_in_time', 'N/A')
                if check_in_time and check_in_time != 'N/A':
                    try:
                        if ' ' in check_in_time:
                            time_display = check_in_time.split(' ')[1][:8]  # Get HH:MM:SS
                        else:
                            time_display = check_in_time[:8]
                    except:
                        time_display = check_in_time
                else:
                    time_display = 'N/A'

            table_data.append([
                str(i),
                student.get('name', 'N/A'),
                student.get('room_no', 'N/A'),
                student.get('branch', 'N/A'),
                str(student.get('year', 'N/A')),
                time_display
            ])
        
        # Create table
        attendance_table = Table(table_data, colWidths=[0.5*inch, 2*inch, 1*inch, 1.5*inch, 0.7*inch, 1.3*inch])
        attendance_table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            
            # Data styling
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            
            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        story.append(attendance_table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _build_footer(self):
        """Build PDF footer"""
        story = []
        
        story.append(Spacer(1, 30))
        
        # Footer information with timezone
        generation_timestamp = format_timestamp(get_current_timestamp(), 'admin')
        footer_text = f"""
        <br/><br/>
        Report generated by Hostel Attendance System 🏠<br/>
        Generated on: {generation_timestamp} IST<br/>
        System Version: 2.0 with Real-time Timestamps | For official use only
        """
        
        story.append(Paragraph(footer_text, self.footer_style))
        
        return story
