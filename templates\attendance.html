{% extends "base.html" %}

{% block title %}Adventure Redirect 🚀{% endblock %}

{% block extra_css %}
<style>
    .redirect-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 25px;
        padding: 3rem 2rem;
        color: white;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;
    }

    .redirect-container::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 4s infinite;
    }

    .redirect-content {
        position: relative;
        z-index: 2;
    }

    .redirect-animation {
        font-size: 4rem;
        margin-bottom: 2rem;
        animation: bounce 2s infinite;
    }

    .redirect-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .redirect-subtitle {
        font-size: 1.3rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }

    .adventure-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.5);
        color: white;
        padding: 1rem 2rem;
        border-radius: 20px;
        font-size: 1.2rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        margin: 0.5rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .adventure-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    .adventure-btn.primary {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        border-color: transparent;
    }

    .adventure-btn.primary:hover {
        background: linear-gradient(135deg, #ff5252 0%, #d84315 100%);
        color: white;
    }

    .countdown {
        font-size: 1.1rem;
        margin-top: 2rem;
        opacity: 0.8;
    }

    @keyframes shimmer {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-20px); }
        60% { transform: translateY(-10px); }
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="redirect-container">
            <div class="redirect-content">
                <div class="redirect-animation">🎮</div>
                <h1 class="redirect-title">Adventure Awaits!</h1>
                <p class="redirect-subtitle">
                    Your daily check-in quest has been upgraded to an epic adventure experience!
                </p>

                <div class="mb-4">
                    <p style="font-size: 1.1rem; margin-bottom: 2rem;">
                        🌟 New features: Magical email authentication, progress tracking,
                        celebratory animations, and achievement unlocks!
                    </p>
                </div>

                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ url_for('login_attendance') }}" class="adventure-btn primary">
                        <span style="font-size: 1.3rem; margin-right: 0.5rem;">🚀</span>
                        Start Your Adventure
                    </a>
                    <a href="{{ url_for('index') }}" class="adventure-btn">
                        <span style="font-size: 1.3rem; margin-right: 0.5rem;">🏠</span>
                        Adventure Hub
                    </a>
                </div>

                <div class="countdown">
                    <span style="font-size: 1.3rem;">⏰</span>
                    Auto-redirecting to adventure in <span id="countdown">5</span> seconds...
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// Auto-redirect countdown
let countdown = 5;
const countdownElement = document.getElementById('countdown');

const countdownInterval = setInterval(() => {
    countdown--;
    countdownElement.textContent = countdown;

    if (countdown <= 0) {
        clearInterval(countdownInterval);
        window.location.href = "{{ url_for('login_attendance') }}";
    }
}, 1000);

// Allow manual navigation to cancel auto-redirect
document.querySelectorAll('.adventure-btn').forEach(btn => {
    btn.addEventListener('click', () => {
        clearInterval(countdownInterval);
    });
});
</script>
{% endblock %}
